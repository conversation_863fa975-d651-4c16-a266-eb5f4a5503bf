#!/usr/bin/env node
/**
 * MASTER COMBO ANALYZER - Single Point of Analysis
 * 
 * This is your centralized analyzer for all combo testing.
 * It processes all combos in INCOMING_COMBOS and generates:
 * - Master combo summaries
 * - Individual combo reports  
 * - Contract projections
 * - Hourly analysis
 * - PM vs Regular comparison
 * - Combined daily totals
 * 
 * Usage: node analyze_all_combos.js
 * Output: C:/backtest-results/comprehensive_analysis_[timestamp]/
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 MASTER COMBO ANALYZER - CENTRALIZED TESTING SYSTEM');
console.log('====================================================');
console.log('📊 Processing all combos from INCOMING_COMBOS...');
console.log('📁 Results will be organized in C:/backtest-results/');
console.log('');

// Ensure results directory exists
const resultsBaseDir = 'C:/backtest-results';
if (!fs.existsSync(resultsBaseDir)) {
  try {
    fs.mkdirSync(resultsBaseDir, { recursive: true });
    console.log(`✅ Created results directory: ${resultsBaseDir}`);
  } catch (error) {
    console.log(`❌ Could not create results directory: ${error.message}`);
    console.log('📁 Using local directory instead...');
  }
}

// Import and run the comprehensive analyzer
const { ComprehensiveComboAnalyzer } = require('./comprehensive_combo_analyzer_with_overlap_exclusion.js');

async function runMasterAnalysis() {
  try {
    console.log('🔄 Starting comprehensive analysis...');
    
    // Run the comprehensive analyzer
    const analyzer = new ComprehensiveComboAnalyzer();
    await analyzer.analyzeCombos();
    
    console.log('');
    console.log('🎉 ANALYSIS COMPLETE!');
    console.log('====================');
    console.log('✅ All combo data processed');
    console.log('✅ Master summaries generated');
    console.log('✅ Individual combo reports created');
    console.log('✅ Contract projections calculated');
    console.log('✅ Hourly analysis completed');
    console.log('✅ PM vs Regular comparison done');
    console.log('');
    console.log('📁 Check the output directory for all reports');
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    console.error(error.stack);
  }
}

// Clean up old test results in main directory (optional)
function cleanupOldResults() {
  const currentDir = '.';
  const files = fs.readdirSync(currentDir);
  
  // Look for old analysis directories and random result files
  const oldAnalysisFiles = files.filter(file => {
    return file.includes('analysis_') || 
           file.includes('combo_reports_') ||
           file.includes('comprehensive_analysis_') ||
           file.includes('detailed_hourly_analysis_') ||
           file.includes('hourly_monthly_analysis_') ||
           file.includes('recent_performance_analysis_') ||
           (file.endsWith('.json') && file.includes('results')) ||
           (file.endsWith('.txt') && file.includes('ANALYSIS'));
  });
  
  if (oldAnalysisFiles.length > 0) {
    console.log('🧹 Found old analysis files in main directory:');
    oldAnalysisFiles.forEach(file => console.log(`   - ${file}`));
    console.log('');
    console.log('💡 Consider moving these to C:/backtest-results/ for organization');
    console.log('');
  }
}

// Main execution
if (require.main === module) {
  cleanupOldResults();
  runMasterAnalysis();
}

module.exports = { runMasterAnalysis };
