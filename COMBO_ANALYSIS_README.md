# CENTRALIZED COMBO ANALYSIS SYSTEM

## Overview
This is your single, unified analyzer for all combo testing. All results are now organized in `C:\backtest-results\` to keep your main directory clean.

## Quick Start
```bash
# Run the master analyzer
node analyze_all_combos.js

# Or use the batch file
run_combo_analysis.bat
```

## What It Does
The master analyzer processes all combos in `INCOMING_COMBOS\` and generates:

### 📊 Master Reports
- **MASTER_COMBO_ANALYSIS.txt** - Complete ranking of all combos
- **PM_COMPARISON_ANALYSIS.txt** - PM vs Regular combo comparison
- **OVERLAP_ANALYSIS.txt** - Hour overlap analysis
- **COMBINED_DAILY_TOTALS.txt** - Daily P&L summaries

### 📈 Detailed Analysis
- **HOURLY_ANALYSIS.txt** - Hour-by-hour performance breakdown
- **CONTRACT_PROJECTIONS.txt** - Scaling projections (3, 5, 10, 25, 50, 100 contracts)

### 📁 Output Structure
```
C:\backtest-results\
└── comprehensive_analysis_[timestamp]\
    ├── MASTER_COMBO_ANALYSIS.txt
    ├── PM_COMPARISON_ANALYSIS.txt
    ├── OVERLAP_ANALYSIS.txt
    ├── CONTRACT_PROJECTIONS.txt
    ├── HOURLY_ANALYSIS.txt
    └── COMBINED_DAILY_TOTALS.txt
```

## Key Features
- ✅ Processes ALL combos in INCOMING_COMBOS
- ✅ Includes both regular and PM combos
- ✅ Generates master summaries with rankings
- ✅ Creates individual combo breakdowns
- ✅ Calculates contract size projections
- ✅ Provides hourly performance analysis
- ✅ Compares PM vs Regular strategies
- ✅ Organizes all output in centralized location

## Input Data
The analyzer reads from:
- `INCOMING_COMBOS\SL*_TP*\` - Regular combo directories
- `INCOMING_COMBOS\SL*_TP*__PM*\` - PM combo directories

Each directory should contain daily JSON files (e.g., `20240815.json`)

## Configuration
Edit `comprehensive_combo_analyzer_with_overlap_exclusion.js` to modify:
- Contract sizes for projections
- Output directory location
- Excluded directories
- Selected winner combo

## Previous Analysis Files
Your backtest-bot folder had many scattered analysis files. Consider moving these to `C:\backtest-results\archive\` for organization:
- `combo_analysis_reports*`
- `comprehensive_analysis_*`
- `detailed_hourly_analysis_*`
- `hourly_monthly_analysis_*`
- Various `.json` and `.txt` result files

## Usage Tips
1. **Daily Analysis**: Run after adding new combo results to INCOMING_COMBOS
2. **Monthly Review**: Use for comprehensive strategy evaluation
3. **Live Trading**: Reference CONTRACT_PROJECTIONS.txt for position sizing
4. **Strategy Selection**: Use MASTER_COMBO_ANALYSIS.txt for ranking

## Support
This system replaces all previous scattered analyzers with one comprehensive solution. All the functionality from your master combo summaries, individual summaries, and projections is now centralized here.
