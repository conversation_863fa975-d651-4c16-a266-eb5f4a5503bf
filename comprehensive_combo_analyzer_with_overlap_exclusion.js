const fs = require('fs');
const path = require('path');

console.log('🚀 COMPREHENSIVE COMBO ANALYZER WITH OVERLAP EXCLUSION');
console.log('======================================================');

const CONFIG = {
  incomingDir: 'INCOMING_COMBOS',
  outDir: `C:/backtest-results/comprehensive_analysis_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}`,

  // No overlap hour exclusion - include all hours
  overlapHour: null,

  // Position sizing for projections
  contractSizes: [3, 5, 10, 25, 50, 100], // micro contracts

  // Exclude analysis directories
  excludeDirs: ['analysis'],

  // Selected winner
  selectedWinner: 'SL10_TP10'
};

class ComprehensiveComboAnalyzer {
  constructor() {
    this.combos = [];
    this.pmCombos = [];
    this.regularCombos = [];
    this.allComboData = new Map();
  }

  cleanComboName(comboName) {
    return comboName
      .replace('__PM2', '_PM2')
      .replace('__PM', '_PM')
      .replace('__ALT2', '_2')
      .replace('__ALT', '_ALT')
      .replace('_ALT', '_ALT');
  }

  isPMCombo(comboName) {
    return comboName.includes('__PM2') || comboName.includes('__PM');
  }

  async analyzeCombos() {
    console.log('📊 Starting comprehensive combo analysis...');
    
    if (!fs.existsSync(CONFIG.outDir)) {
      fs.mkdirSync(CONFIG.outDir, { recursive: true });
    }

    // Get all combo directories
    const comboDirectories = fs.readdirSync(CONFIG.incomingDir)
      .filter(dir => fs.statSync(path.join(CONFIG.incomingDir, dir)).isDirectory())
      .filter(dir => !CONFIG.excludeDirs.includes(dir));

    console.log(`📁 Found ${comboDirectories.length} combo directories`);

    // Separate PM and regular combos
    const pmCombos = comboDirectories.filter(combo => this.isPMCombo(combo));
    const regularCombos = comboDirectories.filter(combo => !this.isPMCombo(combo));

    console.log(`🌅 PM Combos: ${pmCombos.length}`);
    console.log(`🌞 Regular Combos: ${regularCombos.length}`);

    // Analyze all combos
    for (const combo of comboDirectories) {
      console.log(`🔍 Analyzing ${combo}...`);
      await this.analyzeCombo(combo);
    }

    // Generate comprehensive reports
    await this.generateMasterComboReport();
    await this.generatePMComparisonReport();
    await this.generateOverlapAnalysisReport();
    await this.generateProjectionReports();
    await this.generateHourlyAnalysis();
    await this.generateCombinedDailyTotals();

    console.log(`✅ Comprehensive analysis complete! Reports saved to: ${CONFIG.outDir}`);
  }

  async analyzeCombo(comboName) {
    const comboPath = path.join(CONFIG.incomingDir, comboName);
    const files = fs.readdirSync(comboPath).filter(f => f.endsWith('.json'));

    const comboData = {
      name: this.cleanComboName(comboName),
      originalName: comboName,
      isPM: this.isPMCombo(comboName),
      totalTrades: 0,
      wins: 0,
      losses: 0,
      totalPnL: 0,
      tradingDays: 0,
      profitableDays: 0,
      bestDay: { date: '', pnl: -Infinity },
      worstDay: { date: '', pnl: Infinity },
      maxDrawdown: 0,
      hourlyDistribution: {},
      monthlyBreakdown: {},
      dailyPnLs: []
    };

    // Initialize hourly distribution
    for (let hour = 0; hour < 24; hour++) {
      comboData.hourlyDistribution[hour] = {
        trades: 0,
        wins: 0,
        totalPnL: 0
      };
    }

    for (const file of files) {
      const filePath = path.join(comboPath, file);
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      
      const date = file.replace('.json', '');
      let dayPnL = 0;
      let dayTrades = 0;
      comboData.tradingDays++;

      if (Array.isArray(data)) {
        for (const trade of data) {
          const hour = trade.entryHourUTC || 0;
          
          // Include all hours - no exclusion

          const pnl = trade.pnl || 0;
          dayTrades++;

          // Overall stats
          comboData.totalTrades++;
          comboData.totalPnL += pnl;
          
          if (pnl > 0) {
            comboData.wins++;
            comboData.hourlyDistribution[hour].wins++;
          } else if (pnl < 0) {
            comboData.losses++;
          }

          // Hourly distribution
          comboData.hourlyDistribution[hour].trades++;
          comboData.hourlyDistribution[hour].totalPnL += pnl;

          dayPnL += pnl;
        }
      }

      // Daily stats
      comboData.dailyPnLs.push({ date, pnl: dayPnL, trades: dayTrades });
      
      if (dayPnL > 0) {
        comboData.profitableDays++;
      }

      if (dayPnL > comboData.bestDay.pnl) {
        comboData.bestDay = { date, pnl: dayPnL };
      }

      if (dayPnL < comboData.worstDay.pnl) {
        comboData.worstDay = { date, pnl: dayPnL };
      }

      // Track drawdown (simplified)
      if (dayPnL < 0 && Math.abs(dayPnL) > comboData.maxDrawdown) {
        comboData.maxDrawdown = Math.abs(dayPnL);
      }
    }

    // Calculate derived metrics
    comboData.winRate = comboData.totalTrades > 0 ? (comboData.wins / comboData.totalTrades) * 100 : 0;
    comboData.dayWinRate = comboData.tradingDays > 0 ? (comboData.profitableDays / comboData.tradingDays) * 100 : 0;
    comboData.avgPerTrade = comboData.totalTrades > 0 ? comboData.totalPnL / comboData.totalTrades : 0;
    comboData.avgPerDay = comboData.tradingDays > 0 ? comboData.totalPnL / comboData.tradingDays : 0;

    this.combos.push(comboData);
    this.allComboData.set(comboName, comboData);

    if (comboData.isPM) {
      this.pmCombos.push(comboData);
    } else {
      this.regularCombos.push(comboData);
    }

    console.log(`  ✅ ${comboName}: ${comboData.totalTrades} trades, $${comboData.totalPnL.toFixed(0)} total P&L`);
  }

  async generateMasterComboReport() {
    console.log('📊 Generating master combo report...');

    // Sort all combos by total P&L
    const sortedCombos = [...this.combos].sort((a, b) => b.totalPnL - a.totalPnL);

    let report = '🏆 MASTER COMBO ANALYSIS - ALL HOURS INCLUDED\n';
    report += '='.repeat(100) + '\n\n';
    report += `📊 Analysis Date: ${new Date().toISOString().slice(0, 19)}\n`;
    report += `📁 Source: ${CONFIG.incomingDir}\n`;
    report += `✅ All trading hours included - no exclusions\n\n`;
    
    report += `📈 PORTFOLIO SUMMARY:\n`;
    report += `Total Combos: ${this.combos.length}\n`;
    report += `Regular Combos: ${this.regularCombos.length}\n`;
    report += `PM Combos: ${this.pmCombos.length}\n`;
    report += `Selected Winner: ${CONFIG.selectedWinner}\n\n`;

    // Overall rankings
    report += '🏆 MASTER RANKINGS BY TOTAL P&L:\n';
    report += '-'.repeat(140) + '\n';
    report += 'Rank | Combo                | Type | Total P&L | Days | Prof Days | Day WR | Trade WR | Avg/Day | Trades | Best Day | Max DD  | Avg/Trade\n';
    report += '-'.repeat(140) + '\n';

    sortedCombos.forEach((combo, index) => {
      const rank = (index + 1).toString().padStart(4);
      const name = combo.name.padEnd(20);
      const type = combo.isPM ? 'PM  ' : 'REG ';
      const pnl = `$${combo.totalPnL.toFixed(0)}`.padStart(10);
      const days = combo.tradingDays.toString().padStart(4);
      const profDays = combo.profitableDays.toString().padStart(9);
      const dayWR = `${combo.dayWinRate.toFixed(1)}%`.padStart(6);
      const tradeWR = `${combo.winRate.toFixed(1)}%`.padStart(8);
      const avgDay = `$${combo.avgPerDay.toFixed(0)}`.padStart(7);
      const trades = combo.totalTrades.toString().padStart(6);
      const bestDay = `$${combo.bestDay.pnl.toFixed(0)}`.padStart(8);
      const maxDD = `$${combo.maxDrawdown.toFixed(0)}`.padStart(7);
      const avgTrade = `$${combo.avgPerTrade.toFixed(1)}`.padStart(9);

      const selected = combo.name === CONFIG.selectedWinner ? ' ⭐' : '';

      report += `${rank} | ${name} | ${type} | ${pnl} | ${days} | ${profDays} | ${dayWR} | ${tradeWR} | ${avgDay} | ${trades} | ${bestDay} | ${maxDD} | ${avgTrade}${selected}\n`;
    });

    report += '\n\n';

    // Top performers by category
    const topByPnL = sortedCombos.slice(0, 10);
    const topByWinRate = [...this.combos].sort((a, b) => b.winRate - a.winRate).slice(0, 10);
    const topByAvgDay = [...this.combos].sort((a, b) => b.avgPerDay - a.avgPerDay).slice(0, 10);

    report += '🥇 TOP 10 BY TOTAL P&L:\n';
    report += '-'.repeat(60) + '\n';
    topByPnL.forEach((combo, index) => {
      const type = combo.isPM ? '[PM]' : '[REG]';
      report += `${index + 1}. ${combo.name} ${type}: $${combo.totalPnL.toFixed(0)} (${combo.winRate.toFixed(1)}% WR)\n`;
    });

    report += '\n🎯 TOP 10 BY WIN RATE:\n';
    report += '-'.repeat(60) + '\n';
    topByWinRate.forEach((combo, index) => {
      const type = combo.isPM ? '[PM]' : '[REG]';
      report += `${index + 1}. ${combo.name} ${type}: ${combo.winRate.toFixed(1)}% WR ($${combo.totalPnL.toFixed(0)} total)\n`;
    });

    report += '\n💰 TOP 10 BY DAILY AVERAGE:\n';
    report += '-'.repeat(60) + '\n';
    topByAvgDay.forEach((combo, index) => {
      const type = combo.isPM ? '[PM]' : '[REG]';
      report += `${index + 1}. ${combo.name} ${type}: $${combo.avgPerDay.toFixed(0)}/day (${combo.winRate.toFixed(1)}% WR)\n`;
    });

    fs.writeFileSync(path.join(CONFIG.outDir, 'MASTER_COMBO_ANALYSIS.txt'), report);
  }

  async generatePMComparisonReport() {
    console.log('🌅 Generating PM comparison report...');

    let report = '🌅 PM COMBOS vs REGULAR COMBOS DETAILED COMPARISON\n';
    report += '='.repeat(80) + '\n\n';
    report += `✅ All hours included - complete performance comparison\n\n`;

    // Find matching pairs
    const pairs = [];
    
    for (const pmCombo of this.pmCombos) {
      const baseName = pmCombo.name.replace('_PM', '');
      const regularMatch = this.regularCombos.find(reg => reg.name === baseName);
      
      if (regularMatch) {
        pairs.push({
          baseName,
          pm: pmCombo,
          regular: regularMatch,
          difference: pmCombo.totalPnL - regularMatch.totalPnL,
          combinedPnL: pmCombo.totalPnL + regularMatch.totalPnL
        });
      }
    }

    report += `📊 Found ${pairs.length} matching PM/Regular pairs:\n\n`;

    // Sort pairs by combined P&L
    pairs.sort((a, b) => b.combinedPnL - a.combinedPnL);

    report += 'DIRECT COMPARISONS (Sorted by Combined P&L):\n';
    report += '-'.repeat(120) + '\n';
    report += 'Strategy         | Regular P&L | PM P&L    | Combined  | Difference | Reg WR | PM WR | Reg Avg/Day | PM Avg/Day\n';
    report += '-'.repeat(120) + '\n';

    pairs.forEach(pair => {
      const name = pair.baseName.padEnd(16);
      const regPnL = `$${pair.regular.totalPnL.toFixed(0)}`.padStart(11);
      const pmPnL = `$${pair.pm.totalPnL.toFixed(0)}`.padStart(9);
      const combined = `$${pair.combinedPnL.toFixed(0)}`.padStart(9);
      const diff = `$${pair.difference.toFixed(0)}`.padStart(10);
      const regWR = `${pair.regular.winRate.toFixed(1)}%`.padStart(6);
      const pmWR = `${pair.pm.winRate.toFixed(1)}%`.padStart(5);
      const regAvg = `$${pair.regular.avgPerDay.toFixed(0)}`.padStart(11);
      const pmAvg = `$${pair.pm.avgPerDay.toFixed(0)}`.padStart(10);

      report += `${name} | ${regPnL} | ${pmPnL} | ${combined} | ${diff} | ${regWR} | ${pmWR} | ${regAvg} | ${pmAvg}\n`;
    });

    report += '\n\n';

    // Summary statistics
    const totalRegularPnL = this.regularCombos.reduce((sum, combo) => sum + combo.totalPnL, 0);
    const totalPMPnL = this.pmCombos.reduce((sum, combo) => sum + combo.totalPnL, 0);
    const avgRegularWR = this.regularCombos.reduce((sum, combo) => sum + combo.winRate, 0) / this.regularCombos.length;
    const avgPMWR = this.pmCombos.reduce((sum, combo) => sum + combo.winRate, 0) / this.pmCombos.length;
    const totalCombinedPnL = totalRegularPnL + totalPMPnL;

    report += 'PORTFOLIO SUMMARY STATISTICS:\n';
    report += '-'.repeat(60) + '\n';
    report += `Total Regular P&L: $${totalRegularPnL.toFixed(0)}\n`;
    report += `Total PM P&L: $${totalPMPnL.toFixed(0)}\n`;
    report += `Combined Portfolio P&L: $${totalCombinedPnL.toFixed(0)}\n`;
    report += `PM Contribution: ${(totalPMPnL / totalCombinedPnL * 100).toFixed(1)}%\n`;
    report += `Average Regular Win Rate: ${avgRegularWR.toFixed(1)}%\n`;
    report += `Average PM Win Rate: ${avgPMWR.toFixed(1)}%\n`;
    report += `PM vs Regular P&L Ratio: ${(totalPMPnL / totalRegularPnL).toFixed(3)}x\n\n`;

    // Best combined strategies
    report += 'TOP 10 COMBINED STRATEGIES:\n';
    report += '-'.repeat(60) + '\n';
    pairs.slice(0, 10).forEach((pair, index) => {
      report += `${index + 1}. ${pair.baseName}: $${pair.combinedPnL.toFixed(0)} combined\n`;
      report += `   Regular: $${pair.regular.totalPnL.toFixed(0)} (${pair.regular.winRate.toFixed(1)}% WR)\n`;
      report += `   PM: $${pair.pm.totalPnL.toFixed(0)} (${pair.pm.winRate.toFixed(1)}% WR)\n\n`;
    });

    fs.writeFileSync(path.join(CONFIG.outDir, 'PM_COMPARISON_ANALYSIS.txt'), report);
  }

  async generateOverlapAnalysisReport() {
    console.log('⚠️ Generating overlap analysis report...');

    let report = '⚠️ TRADING HOURS OVERLAP ANALYSIS\n';
    report += '='.repeat(60) + '\n\n';
    report += `🚨 OVERLAP HOUR EXCLUDED: ${CONFIG.overlapHour} UTC (12pm CST)\n`;
    report += `This hour is excluded from PM combo calculations to prevent double-counting.\n\n`;

    // Analyze hourly distribution
    const regularHours = new Set();
    const pmHours = new Set();
    const overlapTrades = { regular: 0, pm: 0 };

    // Collect active hours for each type
    this.regularCombos.forEach(combo => {
      Object.entries(combo.hourlyDistribution).forEach(([hour, data]) => {
        if (data.trades > 0) {
          regularHours.add(parseInt(hour));
          if (parseInt(hour) === CONFIG.overlapHour) {
            overlapTrades.regular += data.trades;
          }
        }
      });
    });

    this.pmCombos.forEach(combo => {
      Object.entries(combo.hourlyDistribution).forEach(([hour, data]) => {
        if (data.trades > 0) {
          pmHours.add(parseInt(hour));
          if (parseInt(hour) === CONFIG.overlapHour) {
            overlapTrades.pm += data.trades;
          }
        }
      });
    });

    const overlappingHours = [...regularHours].filter(hour => pmHours.has(hour));

    report += `🌞 Regular Combo Active Hours: ${[...regularHours].sort().join(', ')}\n`;
    report += `🌅 PM Combo Active Hours: ${[...pmHours].sort().join(', ')}\n`;
    report += `⚠️  Overlapping Hours: ${overlappingHours.sort().join(', ')}\n\n`;

    report += `📊 OVERLAP HOUR ${CONFIG.overlapHour} STATISTICS:\n`;
    report += `Regular trades in hour ${CONFIG.overlapHour}: ${overlapTrades.regular}\n`;
    report += `PM trades in hour ${CONFIG.overlapHour}: ${overlapTrades.pm} (EXCLUDED from analysis)\n`;
    report += `Total excluded trades: ${overlapTrades.pm}\n\n`;

    report += '✅ OVERLAP RESOLUTION:\n';
    report += `By excluding hour ${CONFIG.overlapHour} from PM combos, we ensure:\n`;
    report += '• No double-counting of trades\n';
    report += '• Clean separation between Regular and PM strategies\n';
    report += '• Accurate combined portfolio calculations\n';
    report += '• Safe simultaneous operation of both strategy types\n\n';

    // Hour-by-hour analysis
    report += 'HOUR-BY-HOUR BREAKDOWN:\n';
    report += '-'.repeat(80) + '\n';
    report += 'Hour | Regular Trades | PM Trades | Total | Status\n';
    report += '-'.repeat(80) + '\n';

    for (let hour = 0; hour < 24; hour++) {
      const regTrades = this.regularCombos.reduce((sum, combo) => 
        sum + (combo.hourlyDistribution[hour]?.trades || 0), 0);
      const pmTrades = this.pmCombos.reduce((sum, combo) => 
        sum + (combo.hourlyDistribution[hour]?.trades || 0), 0);
      const total = regTrades + pmTrades;
      
      let status = 'CLEAR';
      if (hour === CONFIG.overlapHour) {
        status = 'EXCLUDED';
      } else if (regTrades > 0 && pmTrades > 0) {
        status = 'OVERLAP';
      }

      if (total > 0 || hour === CONFIG.overlapHour) {
        const hourStr = hour.toString().padStart(4);
        const regStr = regTrades.toString().padStart(14);
        const pmStr = pmTrades.toString().padStart(9);
        const totalStr = total.toString().padStart(5);
        const statusStr = status.padStart(8);

        report += `${hourStr} | ${regStr} | ${pmStr} | ${totalStr} | ${statusStr}\n`;
      }
    }

    fs.writeFileSync(path.join(CONFIG.outDir, 'OVERLAP_ANALYSIS.txt'), report);
  }

  async generateProjectionReports() {
    console.log('💰 Generating projection reports...');

    const sortedCombos = [...this.combos].sort((a, b) => b.totalPnL - a.totalPnL);
    const topCombos = sortedCombos.slice(0, 20);

    let report = '💰 CONTRACT SIZE PROJECTION REPORTS\n';
    report += '='.repeat(80) + '\n\n';

    for (const contractSize of CONFIG.contractSizes) {
      report += `📊 PROJECTIONS FOR ${contractSize} MICRO CONTRACTS:\n`;
      report += '-'.repeat(100) + '\n';
      report += 'Rank | Combo                | Type | Daily Avg | Monthly   | Annual     | Max DD    | Win Rate\n';
      report += '-'.repeat(100) + '\n';

      topCombos.forEach((combo, index) => {
        const multiplier = contractSize / 3; // Base is 3 micros
        const rank = (index + 1).toString().padStart(4);
        const name = combo.name.padEnd(20);
        const type = combo.isPM ? 'PM  ' : 'REG ';
        const daily = `$${(combo.avgPerDay * multiplier).toFixed(0)}`.padStart(9);
        const monthly = `$${(combo.avgPerDay * multiplier * 21).toFixed(0)}`.padStart(9);
        const annual = `$${(combo.avgPerDay * multiplier * 252).toFixed(0)}`.padStart(10);
        const maxDD = `$${(combo.maxDrawdown * multiplier).toFixed(0)}`.padStart(9);
        const winRate = `${combo.winRate.toFixed(1)}%`.padStart(8);

        report += `${rank} | ${name} | ${type} | ${daily} | ${monthly} | ${annual} | ${maxDD} | ${winRate}\n`;
      });

      report += '\n';
    }

    fs.writeFileSync(path.join(CONFIG.outDir, 'CONTRACT_PROJECTIONS.txt'), report);
  }

  async generateHourlyAnalysis() {
    console.log('🕐 Generating hourly analysis...');

    let report = '🕐 HOURLY TRADING ANALYSIS\n';
    report += '='.repeat(60) + '\n\n';

    // Aggregate hourly data
    const hourlyData = {};
    for (let hour = 0; hour < 24; hour++) {
      hourlyData[hour] = {
        regularTrades: 0,
        regularPnL: 0,
        pmTrades: 0,
        pmPnL: 0,
        totalTrades: 0,
        totalPnL: 0
      };
    }

    this.regularCombos.forEach(combo => {
      Object.entries(combo.hourlyDistribution).forEach(([hour, data]) => {
        hourlyData[hour].regularTrades += data.trades;
        hourlyData[hour].regularPnL += data.totalPnL;
      });
    });

    this.pmCombos.forEach(combo => {
      Object.entries(combo.hourlyDistribution).forEach(([hour, data]) => {
        hourlyData[hour].pmTrades += data.trades;
        hourlyData[hour].pmPnL += data.totalPnL;
      });
    });

    // Calculate totals
    Object.keys(hourlyData).forEach(hour => {
      const data = hourlyData[hour];
      data.totalTrades = data.regularTrades + data.pmTrades;
      data.totalPnL = data.regularPnL + data.pmPnL;
    });

    report += 'HOURLY BREAKDOWN:\n';
    report += '-'.repeat(90) + '\n';
    report += 'Hour | Reg Trades | Reg P&L   | PM Trades | PM P&L    | Total P&L | CST Time\n';
    report += '-'.repeat(90) + '\n';

    for (let hour = 0; hour < 24; hour++) {
      const data = hourlyData[hour];
      if (data.totalTrades > 0) {
        const hourStr = hour.toString().padStart(4);
        const regTrades = data.regularTrades.toString().padStart(10);
        const regPnL = `$${data.regularPnL.toFixed(0)}`.padStart(9);
        const pmTrades = data.pmTrades.toString().padStart(9);
        const pmPnL = `$${data.pmPnL.toFixed(0)}`.padStart(9);
        const totalPnL = `$${data.totalPnL.toFixed(0)}`.padStart(9);

        // Convert UTC to CST (UTC-6)
        const cstHour = (hour - 6 + 24) % 24;
        const cstTime = `${cstHour.toString().padStart(2, '0')}:00`.padStart(8);

        const excluded = hour === CONFIG.overlapHour ? ' (EXCLUDED)' : '';

        report += `${hourStr} | ${regTrades} | ${regPnL} | ${pmTrades} | ${pmPnL} | ${totalPnL} | ${cstTime}${excluded}\n`;
      }
    }

    // Best trading hours
    const sortedHours = Object.entries(hourlyData)
      .filter(([hour, data]) => data.totalPnL > 0)
      .sort(([,a], [,b]) => b.totalPnL - a.totalPnL)
      .slice(0, 10);

    report += '\n🏆 TOP 10 MOST PROFITABLE HOURS:\n';
    report += '-'.repeat(50) + '\n';
    sortedHours.forEach(([hour, data], index) => {
      const cstHour = (parseInt(hour) - 6 + 24) % 24;
      report += `${index + 1}. Hour ${hour} UTC (${cstHour}:00 CST): $${data.totalPnL.toFixed(0)}\n`;
    });

    fs.writeFileSync(path.join(CONFIG.outDir, 'HOURLY_ANALYSIS.txt'), report);
  }

  async generateCombinedDailyTotals() {
    console.log('📈 Generating combined daily totals...');

    const totalRegularDaily = this.regularCombos.reduce((sum, combo) => sum + combo.avgPerDay, 0);
    const totalPMDaily = this.pmCombos.reduce((sum, combo) => sum + combo.avgPerDay, 0);
    const combinedDaily = totalRegularDaily + totalPMDaily;

    let report = '📈 COMBINED DAILY TOTALS ANALYSIS\n';
    report += '='.repeat(60) + '\n\n';
    report += `✅ All hours included for complete daily performance\n\n`;

    report += 'DAILY AVERAGE PROJECTIONS:\n';
    report += '-'.repeat(50) + '\n';
    report += `Regular Combos Daily Total: $${totalRegularDaily.toFixed(0)}\n`;
    report += `PM Combos Daily Total: $${totalPMDaily.toFixed(0)}\n`;
    report += `Combined Daily Total: $${combinedDaily.toFixed(0)}\n`;
    report += `PM Contribution: ${(totalPMDaily / combinedDaily * 100).toFixed(1)}%\n\n`;

    report += 'ANNUAL PROJECTIONS (252 trading days):\n';
    report += '-'.repeat(50) + '\n';
    report += `Regular Combos Annual: $${(totalRegularDaily * 252).toFixed(0)}\n`;
    report += `PM Combos Annual: $${(totalPMDaily * 252).toFixed(0)}\n`;
    report += `Combined Annual: $${(combinedDaily * 252).toFixed(0)}\n\n`;

    // Contract size projections
    report += 'CONTRACT SIZE PROJECTIONS:\n';
    report += '-'.repeat(80) + '\n';
    report += 'Contracts | Daily Total | Monthly Total | Annual Total\n';
    report += '-'.repeat(80) + '\n';

    CONFIG.contractSizes.forEach(size => {
      const multiplier = size / 3;
      const daily = `$${(combinedDaily * multiplier).toFixed(0)}`.padStart(11);
      const monthly = `$${(combinedDaily * multiplier * 21).toFixed(0)}`.padStart(13);
      const annual = `$${(combinedDaily * multiplier * 252).toFixed(0)}`.padStart(12);

      report += `${size.toString().padStart(9)} | ${daily} | ${monthly} | ${annual}\n`;
    });

    report += '\n🎯 SELECTED WINNER ANALYSIS:\n';
    report += '-'.repeat(40) + '\n';
    const selectedCombo = this.combos.find(c => c.name === CONFIG.selectedWinner);
    if (selectedCombo) {
      report += `Strategy: ${selectedCombo.name}\n`;
      report += `Type: ${selectedCombo.isPM ? 'PM' : 'Regular'}\n`;
      report += `Daily Average: $${selectedCombo.avgPerDay.toFixed(0)}\n`;
      report += `Win Rate: ${selectedCombo.winRate.toFixed(1)}%\n`;
      report += `Total P&L: $${selectedCombo.totalPnL.toFixed(0)}\n`;
      report += `Max Drawdown: $${selectedCombo.maxDrawdown.toFixed(0)}\n`;
    }

    fs.writeFileSync(path.join(CONFIG.outDir, 'COMBINED_DAILY_TOTALS.txt'), report);
  }
}

// Run the comprehensive analysis if called directly
if (require.main === module) {
  const analyzer = new ComprehensiveComboAnalyzer();
  analyzer.analyzeCombos().catch(console.error);
}

// Export for use by other scripts
module.exports = { ComprehensiveComboAnalyzer };
